import os
import sys
import subprocess
import platform

def install_dependencies():
    """Install required dependencies for BlenderGPT."""
    print("Installing BlenderGPT dependencies...")
    
    # Get Blender's Python executable path
    python_exe = sys.executable
    
    # Install required packages
    packages = [
        "openai==1.12.0",
        "python-dotenv==1.0.1"
    ]
    
    for package in packages:
        print(f"Installing {package}...")
        try:
            subprocess.check_call([python_exe, "-m", "pip", "install", package])
            print(f"Successfully installed {package}")
        except subprocess.CalledProcessError:
            print(f"Failed to install {package}. Please install it manually.")
    
    print("\nSetup complete!")
    print("Don't forget to set your OpenAI API key in the addon preferences.")

if __name__ == "__main__":
    install_dependencies()