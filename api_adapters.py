import os
import json
import requests
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Generator, Union
from openai import OpenAI

class CompletionChunk:
    """A simple class to mimic OpenAI's streaming response chunk format"""
    def __init__(self, content: str = None):
        self.choices = [{"delta": {"content": content}}]

class BaseAPIAdapter(ABC):
    """Base adapter class for API compatibility"""
    
    @abstractmethod
    def chat_completions_create(self, model: str, messages: List[Dict[str, str]], 
                               stream: bool = False, max_tokens: int = 2000, 
                               temperature: float = 0.7, **kwargs) -> Union[Dict[str, Any], Generator]:
        """Create chat completions using the API"""
        pass

class OpenAIAdapter(BaseAPIAdapter):
    """Adapter for OpenAI's official API"""
    
    def __init__(self, api_key: str = None, base_url: str = None):
        """Initialize with optional API key and base URL"""
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.client = OpenAI(api_key=self.api_key, base_url=base_url)
    
    def chat_completions_create(self, model: str, messages: List[Dict[str, str]], 
                               stream: bool = False, max_tokens: int = 2000, 
                               temperature: float = 0.7, **kwargs) -> Union[Dict[str, Any], Generator]:
        """Create chat completions using OpenAI's API"""
        return self.client.chat.completions.create(
            model=model,
            messages=messages,
            stream=stream,
            max_tokens=max_tokens,
            temperature=temperature,
            **kwargs
        )

class OpenAICompatibleAdapter(BaseAPIAdapter):
    """Adapter for OpenAI-compatible APIs (e.g., LM Studio, Ollama, etc.)"""
    
    def __init__(self, api_key: str = None, base_url: str = "http://localhost:1234/v1"):
        """Initialize with base URL for the compatible API"""
        self.api_key = api_key  # May not be needed for local APIs
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json"
        }
        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"
    
    def chat_completions_create(self, model: str, messages: List[Dict[str, str]], 
                               stream: bool = False, max_tokens: int = 2000, 
                               temperature: float = 0.7, **kwargs) -> Union[Dict[str, Any], Generator]:
        """Create chat completions using an OpenAI-compatible API"""
        endpoint = f"{self.base_url}/chat/completions"
        
        payload = {
            "model": model,
            "messages": messages,
            "stream": stream,
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        
        # Add any additional parameters
        for key, value in kwargs.items():
            payload[key] = value
        
        if stream:
            response = requests.post(endpoint, json=payload, headers=self.headers, stream=True)
            response.raise_for_status()
            
            def generate_chunks():
                for line in response.iter_lines():
                    if line:
                        line = line.decode('utf-8')
                        if line.startswith('data: '):
                            line = line[6:]  # Remove 'data: ' prefix
                            if line.strip() == '[DONE]':
                                break
                            try:
                                chunk_data = json.loads(line)
                                content = chunk_data.get('choices', [{}])[0].get('delta', {}).get('content')
                                if content is not None:
                                    chunk = CompletionChunk(content)
                                    yield chunk
                            except json.JSONDecodeError:
                                continue
            
            return generate_chunks()
        else:
            response = requests.post(endpoint, json=payload, headers=self.headers)
            response.raise_for_status()
            return response.json()

def get_api_adapter(api_type: str = "openai", api_key: str = None, base_url: str = None) -> BaseAPIAdapter:
    """Factory function to get the appropriate API adapter"""
    if api_type.lower() == "openai":
        return OpenAIAdapter(api_key=api_key, base_url=base_url)
    elif api_type.lower() == "compatible":
        return OpenAICompatibleAdapter(api_key=api_key, base_url=base_url)
    else:
        raise ValueError(f"Unsupported API type: {api_type}")