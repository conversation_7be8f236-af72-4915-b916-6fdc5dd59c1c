# BlenderGPT
![Header](https://user-images.githubusercontent.com/63528145/227160213-6862cd5e-b31f-43ea-a5e5-6cc340a95617.png)

Blender can be controlled using program scripts written in Python. Modern Large Language Models like OpenAI's GPT-4o, GPT-4.1, o3, and o4-mini can generate these Python scripts from simple English and execute them. This plugin provides an easy to use interface that integrates OpenAI's latest models right in the UI, allowing you to use natural language commands to control Blender.

# Note

This addon requires an OpenAI API key to function. You can obtain an API key by signing up at https://platform.openai.com/

**Updated for July 2025: This addon now supports the latest OpenAI models including GPT-4o, GPT-4.1, o3, and o4-mini. Compatible with Blender 4.4.x and later.**

## Installation

1. Clone this repository by clicking `Code > Download ZIP` on GitHub
2. Open Blender 4.4 or later, go to `Edit > Preferences > Add-ons > Install`
3. Select the downloaded ZIP file and click `Install Add-on`
4. Enable the add-on by checking the checkbox next to `GPT-4 Blender Assistant`
5. Paste your OpenAI API key in the Addon preferences menu
   - Alternatively, create a `.env` file in the addon directory with your API key (see `.env.example` for format)
6. To view the code generations in realtime, go to `Window > Toggle System Console`

### API Key Setup

You have two options for setting up your OpenAI API key:

1. **Addon Preferences (Recommended)**: Enter your API key in the addon preferences menu
2. **Environment Variable**: Set the `OPENAI_API_KEY` environment variable in your system
3. **Local .env File**: Create a `.env` file in the addon directory with your API key

For security reasons, we recommend using the addon preferences method as it stores the key securely within Blender's configuration.

### OpenAI Compatible API Support

This addon now supports OpenAI-compatible APIs such as LM Studio, Ollama, and other local or self-hosted models that implement the OpenAI API format. To use a compatible API:

1. In the addon preferences, select "OpenAI Compatible API" as the API type
2. Enter the base URL for your API endpoint (default: http://localhost:1234/v1)
3. Enter your API key if required (some local APIs don't require authentication)

This feature allows you to use local models or alternative AI providers while maintaining the same user experience.

## Usage

1. In the 3D View, open the sidebar (press `N` if not visible) and locate the `GPT-4 Assistant` tab
2. Select your preferred model from the dropdown menu:
   - **GPT-4o**: Balanced performance and cost (recommended for most tasks)
   - **GPT-4.1**: Highest quality results (more expensive)
   - **o3**: Specialized for reasoning tasks
   - **o4-mini**: Fast and cost-effective for simpler tasks
3. Type a natural language command in the input field, e.g., "create a cube at the origin" or "create a procedural landscape with mountains"
4. Click the `Execute` button to generate and execute the Blender Python code
5. View the generated code by clicking the "Show Code" button next to any assistant response


## Requirements

- Blender 4.4.x or later
- OpenAI API key (Accessible at https://platform.openai.com/api-keys)
- Python 3.11+ (included with Blender 4.4+)


## Features

- **Multiple AI Models**: Choose from GPT-4o, GPT-4.1, o3, and o4-mini to balance quality, speed, and cost
- **Temperature Control**: Adjust the creativity level of the AI responses
- **Code Display**: View and edit the generated Python code before execution
- **Chat History**: Keep track of your conversation with the AI assistant
- **Environment Variable Support**: Securely store your API key

## Example Prompts

See the [example_prompts.md](example_prompts.md) file for a comprehensive list of example prompts to get you started.

Some quick examples:
- "Create a procedural landscape with mountains and valleys"
- "Generate a starfield with 100 stars at random positions"
- "Create a red metallic material and apply it to a sphere"
- "Set up a three-point lighting system"

## Demonstration
https://user-images.githubusercontent.com/63528145/227158577-d92c6e8d-df21-4461-a69b-9e7cde8c8dcf.mov

*Note: The demonstration video shows an older version. The current version has an improved UI and supports more advanced models.*
