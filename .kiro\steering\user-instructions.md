---
inclusion: always
---

# Project Guidelines

## Language Conventions
- Always respond to the user in Turkish
- Conduct all internal operations and documentation in global English
- Author project files in English by default (code, README.md, documentation) unless specifically requested otherwise

## Development Standards
- Use appropriate and up-to-date tools (web searches, APIs) to complete tasks
- Ensure all generated code and actions are functional in real-world environments
- Perform web searches when necessary to obtain the latest and most reliable information

## Attribution Guidelines
- Use "inkbytefo" for all author and developer tags
- Reference the user's GitHub username as "inkbytefo" when needed

## File Naming
- Use English for project file names by default (e.g., example.py, readme.md, docs.md)
- Only use Turkish file names when explicitly requested by the user