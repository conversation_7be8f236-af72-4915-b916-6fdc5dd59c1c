import bpy
import re
import os
import sys
from .api_adapters import get_api_adapter


def get_api_key(context, addon_name):
    preferences = context.preferences
    addon_prefs = preferences.addons[addon_name].preferences
    return addon_prefs.api_key


def init_props():
    bpy.types.Scene.gpt4_chat_history = bpy.props.CollectionProperty(type=bpy.types.PropertyGroup)
    bpy.types.Scene.gpt4_model = bpy.props.EnumProperty(
        name="GPT Model",
        description="Select the GPT model to use",
        items=[
            ("gpt-4o", "GPT-4o (powerful, balanced)", "Use GPT-4o"),
            ("gpt-4.1", "GPT-4.1 (powerful, expensive)", "Use GPT-4.1"),
            ("o3", "o3 (reasoning specialist)", "Use o3"),
            ("o4-mini", "o4-mini (fast, efficient)", "Use o4-mini"),
        ],
        default="gpt-4o",
    )
    
    # Add temperature control for more creative or precise results
    bpy.types.Scene.gpt4_temperature = bpy.props.FloatProperty(
        name="Temperature",
        description="Higher values produce more creative results, lower values are more deterministic",
        default=0.7,
        min=0.0,
        max=2.0,
        step=0.1
    )
    
    # Add advanced options toggle
    bpy.types.Scene.gpt4_show_advanced = bpy.props.BoolProperty(
        name="Show Advanced Options",
        description="Show advanced model options",
        default=False
    )
    bpy.types.Scene.gpt4_chat_input = bpy.props.StringProperty(
        name="Message",
        description="Enter your message",
        default="",
    )
    bpy.types.Scene.gpt4_button_pressed = bpy.props.BoolProperty(default=False)
    bpy.types.PropertyGroup.type = bpy.props.StringProperty()
    bpy.types.PropertyGroup.content = bpy.props.StringProperty()

def clear_props():
    del bpy.types.Scene.gpt4_chat_history
    del bpy.types.Scene.gpt4_chat_input
    del bpy.types.Scene.gpt4_button_pressed
    del bpy.types.Scene.gpt4_temperature
    del bpy.types.Scene.gpt4_show_advanced

def generate_blender_code(prompt, chat_history, context, system_prompt, api_type="openai", base_url=None):
    """
    Generate Blender Python code using OpenAI's API or compatible APIs based on a natural language prompt.
    
    Args:
        prompt (str): The natural language prompt describing what to create in Blender
        chat_history (Collection): Collection of previous messages
        context (Context): Blender context
        system_prompt (str): System prompt to guide the AI
        api_type (str): Type of API to use ("openai" or "compatible")
        base_url (str, optional): Base URL for the API endpoint
        
    Returns:
        str: Generated Python code for Blender
    """
    # Get the appropriate API adapter
    api_adapter = get_api_adapter(api_type=api_type, base_url=base_url)
    
    # Prepare the messages for the API call
    messages = [{"role": "system", "content": system_prompt}]
    
    # Add the last 10 messages from chat history
    for message in chat_history[-10:]:
        if message.type == "assistant":
            messages.append({"role": "assistant", "content": "```\n" + message.content + "\n```"})
        else:
            messages.append({"role": message.type.lower(), "content": message.content})

    # Add the current user message
    messages.append({
        "role": "user", 
        "content": f"Can you please write Blender 4.4+ code for me that accomplishes the following task: {prompt}? \n. Do not respond with anything that is not Python code. Do not provide explanations"
    })

    # Create the API request with streaming enabled
    response = api_adapter.chat_completions_create(
        model=context.scene.gpt4_model,
        messages=messages,
        stream=True,
        max_tokens=2000,
        temperature=context.scene.gpt4_temperature,
    )

    try:
        collected_chunks = []
        completion_text = ''
        # iterate through the stream of chunks
        for chunk in response:
            if chunk.choices[0].delta.content is None:
                continue
            collected_chunks.append(chunk)  # save the chunk
            chunk_text = chunk.choices[0].delta.content
            completion_text += chunk_text  # append the text
            print(completion_text, flush=True, end='\r')
        
        # Extract code from markdown code blocks
        code_blocks = re.findall(r'```(?:python)?(.*?)```', completion_text, re.DOTALL)
        if code_blocks:
            completion_text = code_blocks[0].strip()
            # Remove language identifier if present at the beginning
            if completion_text.startswith('python'):
                completion_text = completion_text[6:].lstrip()
        else:
            # If no code blocks found, use the entire response
            # but warn in the console
            print("WARNING: No code blocks found in the response. Using raw text.")
        
        return completion_text
    except Exception as e:
        print(f"Error processing API response: {e}")
        return None

def split_area_to_text_editor(context):
    area = context.area
    for region in area.regions:
        if region.type == 'WINDOW':
            override = {'area': area, 'region': region}
            bpy.ops.screen.area_split(override, direction='VERTICAL', factor=0.5)
            break

    new_area = context.screen.areas[-1]
    new_area.type = 'TEXT_EDITOR'
    return new_area