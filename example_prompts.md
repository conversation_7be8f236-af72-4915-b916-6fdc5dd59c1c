# Example Prompts for BlenderGPT

Here are some example prompts you can use with the BlenderGPT addon:

## Basic Objects

- Create a cube at the origin
- Create 10 spheres in a circle with radius 5
- Create a cylinder with 16 vertices, height 2, and radius 1
- Create a torus with major radius 2 and minor radius 0.5

## Procedural Generation

- Create a procedural landscape with mountains and valleys
- Generate a procedural city with buildings of varying heights
- Create a forest with 20 trees of different sizes
- Generate a starfield with 100 stars at random positions

## Animation

- Create a bouncing ball animation with 30 frames
- Animate a cube rotating 360 degrees over 24 frames
- Create a simple walk cycle for a character
- Make a spiral animation where a sphere follows a spiral path

## Materials and Textures

- Create a red metallic material and apply it to a sphere
- Create a glass material with refraction
- Create a procedural wood texture and apply it to a cube
- Create a checkered pattern material with alternating colors

## Advanced Techniques

- Create a low-poly tree with leaves
- Generate a procedural cloud system
- Create a water surface with realistic waves
- Generate a fractal terrain with multiple levels of detail

## Modifiers

- Create a cube with a subdivision surface modifier
- Apply an array modifier to create a brick wall
- Create a twisted cylinder using the twist modifier
- Create a sphere with a displacement modifier for a moon-like surface

## Lighting and Rendering

- Set up a three-point lighting system
- Create a sunset lighting scene
- Set up a studio lighting environment for product rendering
- Create a night scene with moonlight and ambient occlusion